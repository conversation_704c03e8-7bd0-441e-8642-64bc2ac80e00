{"dns": {"rules": [{"outbound": ["any"], "server": "local"}, {"clash_mode": "全局代理", "server": "remote"}, {"clash_mode": "关闭代理", "server": "local"}, {"rule_set": ["geosite-cn"], "server": "local"}, {"rule_set": ["category-ads-all"], "server": "block"}], "servers": [{"address": "*******", "detour": "节点选择", "tag": "remote"}, {"address": "https://*********/dns-query", "detour": "direct", "tag": "local"}, {"address": "rcode://refused", "tag": "block"}], "final": "remote", "strategy": "ipv4_only", "disable_cache": false}, "experimental": {"cache_file": {"enabled": true}, "clash_api": {"default_mode": "海外代理", "external_controller": "127.0.0.1:9090", "secret": ""}}, "inbounds": [{"auto_route": true, "domain_strategy": "prefer_ipv4", "endpoint_independent_nat": true, "address": ["**********/30", "2001:0470:f9da:fdfa::1/64"], "mtu": 9000, "sniff_override_destination": true, "stack": "system", "strict_route": true, "type": "tun"}, {"domain_strategy": "prefer_ipv4", "listen": "127.0.0.1", "listen_port": 2333, "sniff": true, "sniff_override_destination": true, "tag": "socks-in", "type": "socks", "users": []}, {"domain_strategy": "prefer_ipv4", "listen": "127.0.0.1", "listen_port": 2334, "sniff": true, "sniff_override_destination": true, "tag": "mixed-in", "type": "mixed", "users": []}], "outbounds": [{"type": "selector", "tag": "节点选择", "outbounds": ["自动选择"]}, {"type": "urltest", "tag": "自动选择", "outbounds": []}, {"type": "direct", "tag": "direct"}], "route": {"auto_detect_interface": true, "rules": [{"action": "sniff"}, {"protocol": "dns", "action": "hijack-dns"}, {"clash_mode": "关闭代理", "outbound": "direct"}, {"clash_mode": "全局代理", "outbound": "节点选择"}, {"rule_set": ["geosite-cn", "geoip-cn"], "outbound": "direct"}, {"ip_is_private": true, "outbound": "direct"}, {"rule_set": ["category-ads-all"], "action": "reject"}], "rule_set": [{"tag": "geosite-cn", "type": "remote", "format": "binary", "url": "https://raw.githubusercontent.com/SagerNet/sing-geosite/rule-set/geosite-cn.srs", "download_detour": "节点选择"}, {"tag": "category-ads-all", "type": "remote", "format": "binary", "url": "https://raw.githubusercontent.com/SagerNet/sing-geosite/rule-set/geosite-category-ads-all.srs", "download_detour": "节点选择"}, {"tag": "geoip-cn", "type": "remote", "format": "binary", "url": "https://raw.githubusercontent.com/Loyalsoldier/geoip/release/srs/cn.srs", "download_detour": "节点选择"}]}}